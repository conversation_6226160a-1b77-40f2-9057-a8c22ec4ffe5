@extends('admin.layouts.layout')

@section('content')

    <!-- =================================== -->
    <!-- Different data widgets ============ -->
    <!-- =================================== -->
    <div class="widget-list">
        <div class="row">
            <div class="col-md-12 widget-holder">

                <form action="{{ route("admin_user_save", ["id" => @$user_id ?: ""]) }}" method="post" class="has-validation-callback" id="user_frm">

                    <div class="card card-default mb-3">
                        <div class="card-header">
                            <h5 class="card-title mt-0 mb-0">{{ @$user_id ? 'Edit' : 'Create' }} User</h5>
                        </div>


                        <div class="card-body">
                            @if(@$user_id)
                                <input type="hidden" name="user_id" value="{{ $user_id }}">
                            @endif
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">

                            <div class="row">
                                <div class="col-12 col-md-7 col-lg-8 col-xl-9">
                                    <div class="row">
                                    	<div class="col-xl-3 col-lg-4">
                                            @include('admin.form.select_component',  ['name' => 'title', 'options' => $titleOptions, 'value' => (old('title') ?: (@$user['title'] ?: "")), 'label' => "Title"])
                                        </div>
                                        <div class="col-xl-3 col-lg-4">
                                            @include('admin.form.select_component',  ['name' => 'title_after', 'options' => $titleOptions, 'value' => (old('title_after') ?: (@$user['title_after'] ?: "")), 'label' => "Title after"])
                                        </div>
                                        <div class="col-xl-3 col-lg-4">
                                            @include('admin.form.text_component',  ['name' => 'salutation', 'value' => (old('salutation') ?: (@$user['salutation'] ?: "")), 'label' => "Salutation", 'required' => false, 'readonly' => true])
                                        </div>
                                        <div class="col-xl-3 col-lg-6">
                                        	<label for="">&nbsp;</label>
                                        	@include('admin.form.checkbox_component',  ['name' => 'app_is_downloaded', 'checked' => (old('app_is_downloaded') ?: (@$user['app_is_downloaded'] ?: "")), 'label' => "App is Downloaded"])
                                        </div>
                                        <div class="col-xl-3 col-lg-6">
                                        	<label for="">&nbsp;</label>
                                            @include('admin.form.checkbox_component',  ['name' => 'doi_status', 'disabled' => true, 'checked' => (old('doi_status') ?: (@$user['doi_status'] ?: "")), 'label' => "DOI Status"])
                                        </div>
                                        <div class="col-xl-3 col-lg-6">
                                        	<label for="">&nbsp;</label>
                                        	@include('admin.form.checkbox_component',  ['name' => 'newsletter_sub', 'disabled' => true, 'checked' => (old('newsletter_sub') ?: (@$user['newsletter_sub'] ?: "")), 'label' => "Marketing Newsletter"])
                                        </div>
                                        <div class="col-xl-3 col-lg-6">
                                        	<label for="">&nbsp;</label>
                                        	@include('admin.form.checkbox_component',  ['name' => 'club_newsletter_sub', 'disabled' => true, 'checked' => (old('club_newsletter_sub') ?: (@$user['club_newsletter_sub'] ?: "")), 'label' => "Club Newsletter"])
                                        </div>
                                        <div class="col-xl-3 col-lg-6">
                                        	<label for="">&nbsp;</label>
                                        	@include('admin.form.checkbox_component',  ['name' => 'club_status_newsletter_sub', 'disabled' => true, 'checked' => (old('club_status_newsletter_sub') ?: (@$user['club_status_newsletter_sub'] ?: "")), 'label' => "Club Status Newsletter"])
                                        </div>
                                        <div class="col-xl-3 col-lg-6">
                                        	<label for="">&nbsp;</label>
                                        	@include('admin.form.checkbox_component',  ['name' => 'birthday_newsletter_sub', 'disabled' => true, 'checked' => (old('birthday_newsletter_sub') ?: (@$user['birthday_newsletter_sub'] ?: "")), 'label' => "Birthday Newsletter"])
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-xl-3 col-lg-6">
                                            @include('admin.form.text_component',  ['name' => 'first_name', 'value' => (old('first_name') ?: (@$user['first_name'] ?: "")), 'label' => "First Name", 'required' => true])
                                        </div>
                                        <div class="col-xl-3 col-lg-6">
                                            @include('admin.form.text_component',  ['name' => 'last_name', 'value' => (old('last_name') ?: (@$user['last_name'] ?: "")), 'label' => "Last Name", 'required' => true])
                                        </div>
                                        <div class="col-xl-6 col-lg-6">
                                            @include('admin.form.radio_component',  ['name' => 'gender', 'values' => gender_options(), 'checked' => (old('gender') ?: (@$user['gender'] ?: "")), 'label' => "Gender", 'required' => true])
                                        </div>
                                    </div>

                                    <div class="row">
                                    	<div class="col-xl-4 col-lg-6">
                                        	@include('admin.form.text_component',  ['name' => 'email', 'value' => (old('email') ?: (@$user['email'] ?: "")), 'label' => "E-Mail Address", 'required' => true])
                                        </div>
                                        <div class="col-xl-4 col-lg-6">
                                        	@include('admin.form.text_component',  ['name' => 'mobile', 'value' => (old('mobile') ?: (@$user['mobile'] ?: "")), 'label' => "Mobile Phone"])
                                        </div>
                                        <div class="col-xl-4 col-lg-6">
                                        	@include('admin.form.text_component',  ['name' => 'day_of_birth', 'value' => (old('day_of_birth') ?: (@$user['day_of_birth'] ? date("m/d/Y", strtotime(@$user['day_of_birth'])) : "")), 'label' => "Date of Birth", "inputCls" => 'datepicker'])
                                        </div>
                                    </div>

                                    <div class="row">
                                    	<div class="col-xl-4 col-lg-6">
                                        	@include('admin.form.text_component',  ['name' => 'street', 'value' => (old('street') ?: (@$address['street'] ?: "")), 'label' => "Street"])
                                        </div>
                                        <div class="col-xl-2 col-lg-3">
                                        	@include('admin.form.text_component',  ['name' => 'street_number', 'value' => (old('street_number') ?: (@$address['street_number'] ?: "")), 'label' => "No."])
                                        </div>
                                        <div class="col-xl-2 col-lg-3">
                                        	@include('admin.form.text_component',  ['name' => 'street_additional', 'value' => (old('street_additional') ?: (@$address['street_additional'] ?: "")), 'label' => "Top."])
                                        </div>
                                    </div>

                                    <div class="row">
                                    	<div class="col-xl-2 col-lg-3">
                                        	@include('admin.form.text_component',  ['name' => 'post_code', 'value' => (old('post_code') ?: (@$address['post_code'] ?: "")), 'label' => "ZIP"])
                                        </div>
                                        <div class="col-xl-4 col-lg-6">
                                        	@include('admin.form.text_component',  ['name' => 'city', 'value' => (old('city') ?: (@$address['city'] ?: "")), 'label' => "City"])
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-xl-4 col-lg-6">
                                            @include('admin.form.select_component',  ['name' => 'country_id', 'options' => $countryOptions, 'value' => (old('country_id') ?: (@$user['country_id'] ?: "")), 'label' => "Country"])
                                        </div>
                                    </div>

                                    @if(isDevhost() || isLocalhost())
                                    <div class="row">
                                        <div class="col-xl-4 col-lg-6">
                                            @include('admin.form.password_component',  ['name' => 'change_password', 'value' => (old('change_password') ?: ""), 'label' => "Debug - Change password", 'info' => "Leave empty to keep password. Enter 0 to remove password."])
                                        </div>
                                    </div>
                                    @endif
                                </div>

                                <div class="col-12 col-md-5 col-lg-4 col-xl-3">
                                	<div class="card card-default mb-3">
                                        <div class="card-header">
                                            <h6 class="card-title mt-0 mb-0">Meta Data</h6>
                                        </div>
                                        <div class="card-body p-1">
                                            <table class="table mb-0">

                                                @php
                                                $metaDataFields = ["language_id", "member_id", "salesforce_id", "person_contact_id", "status", "next_status", "next_status_amount", "title_after", "is_employee", "employee_info_id", "card_expire_at", "status_expire_at", "deleted_via_gdpr", "test_user", "status_discount", "next_status_percent", "source_of_registration"];
                                                @endphp


                                                @foreach($metaDataFields as $metaDataField)
                                                <tr>
                                                    <td class="td-middle"><strong>{{ ucfirst(str_replace("_", " ", $metaDataField)) }}:</strong></td>
                                                    <td>{{ @$user[$metaDataField] }}</td>
                                                </tr>
                                                @endforeach
                                            </table>
                                        </div>
                                    </div>

                                    <div class="card card-default mb-3">
                                        <div class="card-header">
                                            <h6 class="card-title mt-0 mb-0">Change Data</h6>
                                        </div>

                                        <div class="card-body">
                                        	<p class="mb-1"><strong>Attention:</strong></p>
                                            <p>
                                            	Changing Data will also<br>
                                                submit changes to <br>
                                                Salesforce!<br>
                                            </p>

                                            <div class="d-flex justify-content-between">
                                                <a href="javascript:;" class="btn btn-sm btn-default">Cancel</a>
                                                <button class="btn btn-sm btn-success" type="submit">Update User Data</button>
                                            </div>

                                            <br>

                                            <div class="d-flex justify-content-between">
                                                <button id="usr_rm_from_mw" class="btn btn-sm btn-danger" type="button"
                                                        data-remove_url="{{ route("admin_user_remove", ["id" => @$user_id ?: ""]) }}">Remove from Middleware</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                        </div>
                     </div>
                </form>

                @if(isset($purchases) && count($purchases) > 0)
                    <div class="card card-default mb-3">
                        <div class="card-header">
                            <h5 class="card-title mt-0 mb-0">User Purchases</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Purchase ID</th>
                                            <th>Purchase Date</th>
                                            <th>Total Amount</th>
                                            <th>Last Changed</th>
                                            <th>Updated At</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($purchases as $purchase)
                                            <tr>
                                                <td>{{ $purchase['purchase_id'] }}</td>
                                                <td>{{ date('M d, Y', strtotime($purchase['purchase_date'])) }}</td>
                                                <td>{{ number_format($purchase['total_amount'], 2) }} €</td>
                                                <td>{{ date('M d, Y H:i', strtotime($purchase['last_changed'])) }}</td>
                                                <td>{{ date('M d, Y H:i', strtotime($purchase['updated_at'])) }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                @endif

            </div>
            <!-- /.widget-holder -->


        </div>
        <!-- /.row -->
    </div>
    <!-- /.widget-list -->
@endsection
